// test functions - just run these to check if stuff works

// basic sheet access test
function testSheetAccess() {
  console.log('=== Testing Sheet Access ===');

  try {
    const sheet = SpreadsheetApp.openById(SHEET_ID).getSheetByName(SHEET_NAME);
    console.log('✓ Sheet access successful');
    console.log('Sheet name:', sheet.getName());
    console.log('Sheet dimensions:', sheet.getMaxRows(), 'x', sheet.getMaxColumns());

    // check current values in price row
    const range = sheet.getRange(PRICE_ROW, 2, 1, 7); // B4:H4
    const values = range.getValues()[0];
    console.log('Current values in price row:', values);

    return true;
  } catch (error) {
    console.error('✗ Sheet access failed:', error);
    return false;
  }
}

/**
 * Test updating the sheet with sample data
 */
function testSheetUpdate() {
  console.log('=== Testing Sheet Update ===');
  
  const sampleData = {
    'spine h': 0.018463,  // Should become: 0.018463 * 0.91 * 84000 = 1411 (rounded)
    'spine a': 0.015000,  // Should become: 0.015000 * 0.91 * 84000 = 1147 (rounded)
    'living a': 0.020000, // Should become: 0.020000 * 0.91 * 84000 = 1528 (rounded)
    'thunder a': 0.017500, // Should become: 0.017500 * 0.91 * 84000 = 1337 (rounded)
    'living h': null,     // Should show OFF
    'wild a': 0.016000,   // Should become: 0.016000 * 0.91 * 84000 = 1222 (rounded)
    'thunder h': 0.019000 // Should become: 0.019000 * 0.91 * 84000 = 1452 (rounded)
  };
  
  console.log('Sample data:', sampleData);
  
  const result = updateSheetPrices(sampleData);
  console.log('Update result:', result);
  
  if (result.success) {
    console.log('✓ Sheet update successful');
    console.log(`Updated ${result.updatedCount} prices, ${result.offCount} set to OFF`);
  } else {
    console.error('✗ Sheet update failed:', result.message);
  }
  
  return result.success;
}

/**
 * Test the web app POST endpoint
 */
function testWebAppEndpoint() {
  console.log('=== Testing Web App Endpoint ===');
  
  const testPayload = {
    action: 'updatePrices',
    data: {
      'spine h': 0.001111,
      'spine a': 0.002222,
      'living a': null,
      'thunder a': 0.003333,
      'living h': 0.004444,
      'wild a': null,
      'thunder h': 0.005555
    },
    timestamp: new Date().toISOString()
  };
  
  // Simulate the POST request
  const mockEvent = {
    postData: {
      contents: JSON.stringify(testPayload)
    }
  };
  
  try {
    const response = doPost(mockEvent);
    const responseText = response.getContent();
    const responseData = JSON.parse(responseText);
    
    console.log('Response:', responseData);
    
    if (responseData.success) {
      console.log('✓ Web app endpoint test successful');
    } else {
      console.error('✗ Web app endpoint test failed:', responseData.error);
    }
    
    return responseData.success;
  } catch (error) {
    console.error('✗ Web app endpoint test error:', error);
    return false;
  }
}

/**
 * Run all tests
 */
function runAllTests() {
  console.log('=== Running All Tests ===');
  
  const tests = [
    { name: 'Sheet Access', func: testSheetAccess },
    { name: 'Sheet Update', func: testSheetUpdate },
    { name: 'Web App Endpoint', func: testWebAppEndpoint },
    { name: 'Cata Sheet Access', func: testCataSheetAccess },
    { name: 'Cata Realm Fetch', func: testCataRealmFetch }
  ];
  
  let passedTests = 0;
  
  for (const test of tests) {
    console.log(`\n--- ${test.name} Test ---`);
    try {
      const result = test.func();
      if (result) {
        passedTests++;
        console.log(`✓ ${test.name} test PASSED`);
      } else {
        console.log(`✗ ${test.name} test FAILED`);
      }
    } catch (error) {
      console.error(`✗ ${test.name} test ERROR:`, error);
    }
  }
  
  console.log(`\n=== Test Summary ===`);
  console.log(`Passed: ${passedTests}/${tests.length} tests`);
  
  if (passedTests === tests.length) {
    console.log('🎉 All tests passed! Your setup is ready.');
  } else {
    console.log('⚠️ Some tests failed. Please check the configuration.');
  }
  
  return passedTests === tests.length;
}

/**
 * Clear all price data (set everything to OFF)
 */
function clearAllPrices() {
  console.log('=== Clearing All Prices ===');

  const clearData = {};
  for (const realmKey of Object.keys(REALM_COLUMNS)) {
    clearData[realmKey] = null;
  }

  const result = updateSheetPrices(clearData);
  console.log('Clear result:', result);

  return result.success;
}

// test cata sheet access and settings
function testCataSheetAccess() {
  console.log('=== Testing Cata Sheet Access ===');

  try {
    const spreadsheet = SpreadsheetApp.openById(SHEET_ID);
    const cataSheet = spreadsheet.getSheetByName(CATA_SHEET_NAME);

    if (!cataSheet) {
      console.error(`✗ Cata sheet "${CATA_SHEET_NAME}" not found`);
      return false;
    }

    console.log('✓ Cata sheet access successful');
    console.log('Sheet name:', cataSheet.getName());
    console.log('Sheet dimensions:', cataSheet.getMaxRows(), 'x', cataSheet.getMaxColumns());

    // check settings
    const timestamp = cataSheet.getRange(1, 1).getValue();
    console.log('Current timestamp (A1):', timestamp || 'Not set');

    const pageSize = cataSheet.getRange(3, 1).getValue();
    console.log('Page size setting (A3):', pageSize || 'Not set (will default to 20)');

    const threshold = cataSheet.getRange(5, 1).getValue();
    console.log('Undercut threshold (A5):', threshold || 'Not set (will default to 0)');

    const autoUpdate = cataSheet.getRange(7, 1).getValue();
    console.log('Auto update interval (A7):', autoUpdate || 'Not set (will default to 60)');

    // check realm config
    console.log('Configured Cata realms:', Object.keys(CATA_REALMS).length);
    for (const [key, config] of Object.entries(CATA_REALMS)) {
      console.log(`- ${key}: Column ${config.column} (${config.name})`);
    }

    return true;
  } catch (error) {
    console.error('✗ Cata sheet access failed:', error);
    return false;
  }
}

// test undercut calculation
function testUndercutCalculation() {
  console.log('=== Testing Undercut Calculation ===');

  const testPrices = [0.611000, 0.500000, 1.234567];

  testPrices.forEach(price => {
    const undercut = (price - 0.000001).toFixed(6);
    console.log(`Original: ${price.toFixed(6)} -> Undercut: ${undercut}`);
  });
}

/**
 * Test fetching data from one realm API
 */
function testCataRealmFetch() {
  console.log('=== Testing Cata Realm Data Fetch ===');

  try {
    // Test with Firemaw Alliance (first realm)
    const testRealm = CATA_REALMS['firemaw a'];
    console.log(`Testing realm: ${testRealm.name}`);
    console.log(`URL: ${testRealm.url}`);

    const realmData = fetchRealmData(testRealm.url, 5); // Fetch only 5 for testing

    console.log(`✓ Successfully fetched ${realmData.length} entries`);

    // Show first few entries
    for (let i = 0; i < Math.min(3, realmData.length); i++) {
      const seller = realmData[i];
      console.log(`Entry ${i + 1}:`);
      console.log(`  Shop: ${seller.username}`);
      console.log(`  Stock: ${seller.available_qty}`);
      console.log(`  USD Price: ${seller.unit_price_in_usd || seller.unit_price}`);
      console.log(`  Is Online: ${seller.is_online}`);
    }

    return true;
  } catch (error) {
    console.error('✗ Cata realm fetch failed:', error);
    return false;
  }
}

/**
 * Test updating Cata reports
 */
function testCataReportsUpdate() {
  console.log('=== Testing Cata Reports Update ===');

  try {
    const result = updateCataReports();

    if (result.success) {
      console.log('✓ Cata reports update successful');
      console.log(`Message: ${result.message}`);
      console.log(`Total updated: ${result.totalUpdated}`);
    } else {
      console.error('✗ Cata reports update failed:', result.message);
    }

    return result.success;
  } catch (error) {
    console.error('✗ Cata reports update error:', error);
    return false;
  }
}
