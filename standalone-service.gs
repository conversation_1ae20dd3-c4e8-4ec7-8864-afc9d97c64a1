// Standalone G2G Price Service
// Deploy this as a web app and use the URL in your main sheet

// Configuration
const SHEET_ID = '1FStcYoFPninNRsWX4XvXxHVtrc2usOnJ79R-ep3l38c';
const SHEET_NAME = 'Sheet1';
const CATA_SHEET_NAME = 'Cata';
const PRICE_ROW = 4;
const CATA_START_ROW = 6;

// Realm configurations
const REALM_COLUMNS = {
  'spine a': 2,
  'spine h': 3,
  'living a': 4,
  'living h': 5,
  'thunder a': 6,
  'thunder h': 7,
  'wild a': 8
};

const CATA_REALMS = {
  'firemaw a': {
    name: 'Firemaw Alliance',
    url: 'https://sls.g2g.com/offer/search?seo_term=wow-classic-gold&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41012&sort=lowest_price&page_size=20&group=0&currency=USD&country=TR&v=v2',
    recommendedUrl: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41012&sort=recommended_v2&page_size=60&group=0&currency=USD&country=TR&v=v2',
    column: 2
  },
  'gehennas h': {
    name: 'Gehennas Horde',
    url: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41023&sort=lowest_price&page_size=20&group=0&currency=USD&country=TR&v=v2',
    recommendedUrl: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41023&sort=recommended_v2&page_size=60&group=0&currency=USD&country=TR&v=v2',
    column: 3
  },
  'golemagg h': {
    name: 'Golemagg Horde',
    url: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41029&sort=lowest_price&page_size=20&group=0&currency=USD&country=TR&v=v2',
    recommendedUrl: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41029&sort=recommended_v2&page_size=60&group=0&currency=USD&country=TR&v=v2',
    column: 4
  },
  'everlook a': {
    name: 'Everlook Alliance',
    url: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41003&sort=lowest_price&page_size=20&group=0&currency=USD&country=TR&v=v2',
    recommendedUrl: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41003&sort=recommended_v2&page_size=60&group=0&currency=USD&country=TR&v=v2',
    column: 5
  },
  'venoxis h': {
    name: 'Venoxis Horde',
    url: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41122&sort=lowest_price&page_size=20&group=0&currency=USD&country=TR&v=v2',
    recommendedUrl: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41122&sort=recommended_v2&page_size=60&group=0&currency=USD&country=TR&v=v2',
    column: 6
  },
  'pyrewood village a': {
    name: 'Pyrewood Village Alliance',
    url: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41083&sort=lowest_price&page_size=20&group=0&currency=USD&country=TR&v=v2',
    recommendedUrl: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41083&sort=recommended_v2&page_size=60&group=0&currency=USD&country=TR&v=v2',
    column: 7
  }
};

// Colors
const GREEN_COLOR = '#00FF00';
const RED_COLOR = '#FF0000';
const YELLOW_COLOR = '#FFFF00';
const LIGHT_GRAY = '#F5F5F5';
const IRANIAN_HIGHLIGHT = '#E6B3B3';

/**
 * Main doPost handler for web app
 */
function doPost(e) {
  try {
    console.log('Received POST request');
    
    let requestData;
    try {
      requestData = JSON.parse(e.postData.contents);
    } catch (parseError) {
      console.error('Error parsing request data:', parseError);
      return ContentService
        .createTextOutput(JSON.stringify({
          success: false,
          error: 'Invalid JSON data'
        }))
        .setMimeType(ContentService.MimeType.JSON);
    }
    
    console.log('Request data:', requestData);
    
    if (requestData.action === 'updatePrices') {
      const result = updateSheetPrices(requestData.data);
      return createJsonResponse(result);
    }

    if (requestData.action === 'updateCataReports') {
      const result = updateCataReports();
      return createJsonResponse(result);
    }

    if (requestData.action === 'getPriceData') {
      const result = getPriceDataForRealms(requestData.realms || []);
      return createJsonResponse(result);
    }

    if (requestData.action === 'getCataData') {
      const result = getCataDataForRealms(requestData.realms || []);
      return createJsonResponse(result);
    }
    
    return ContentService
      .createTextOutput(JSON.stringify({
        success: false,
        error: 'Unknown action'
      }))
      .setMimeType(ContentService.MimeType.JSON);
      
  } catch (error) {
    console.error('Error in doPost:', error);
    
    return ContentService
      .createTextOutput(JSON.stringify({
        success: false,
        error: error.toString()
      }))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

/**
 * Handle GET requests
 */
function doGet(e) {
  return ContentService
    .createTextOutput(JSON.stringify({
      status: 'G2G Price Service is running',
      timestamp: new Date().toISOString()
    }))
    .setMimeType(ContentService.MimeType.JSON);
}

function createJsonResponse(result) {
  return ContentService
    .createTextOutput(JSON.stringify({
      success: result.success,
      message: result.message,
      data: result.data || null,
      timestamp: new Date().toISOString()
    }))
    .setMimeType(ContentService.MimeType.JSON);
}

/**
 * Get price data for specific realms (for external consumption)
 */
function getPriceDataForRealms(realms) {
  try {
    const priceData = {};
    
    for (const realm of realms) {
      const realmKey = realm.toLowerCase();
      if (REALM_COLUMNS[realmKey]) {
        // Fetch price from G2G API
        const price = fetchLowestPriceForRealm(realmKey);
        priceData[realmKey] = price;
      }
    }
    
    return {
      success: true,
      message: `Retrieved price data for ${Object.keys(priceData).length} realms`,
      data: priceData
    };
    
  } catch (error) {
    console.error('Error getting price data:', error);
    return {
      success: false,
      message: error.toString()
    };
  }
}

/**
 * Get Cata data for specific realms (for external consumption)
 */
function getCataDataForRealms(realms) {
  try {
    const cataData = {};
    
    for (const realm of realms) {
      const realmKey = realm.toLowerCase().replace(' ', ' ');
      if (CATA_REALMS[realmKey]) {
        const realmConfig = CATA_REALMS[realmKey];
        const data = fetchRealmData(realmConfig.url, 20);
        cataData[realmKey] = {
          name: realmConfig.name,
          data: data.slice(0, 10) // Return top 10 results
        };
      }
    }
    
    return {
      success: true,
      message: `Retrieved Cata data for ${Object.keys(cataData).length} realms`,
      data: cataData
    };
    
  } catch (error) {
    console.error('Error getting Cata data:', error);
    return {
      success: false,
      message: error.toString()
    };
  }
}

/**
 * Fetch lowest price for a specific realm
 */
function fetchLowestPriceForRealm(realmKey) {
  // This would need to be implemented based on your G2G API logic
  // For now, returning null as placeholder
  return null;
}

/**
 * Update sheet with price data
 */
function updateSheetPrices(priceData) {
  try {
    console.log('Updating sheet with price data:', priceData);

    const sheet = SpreadsheetApp.openById(SHEET_ID).getSheetByName(SHEET_NAME);
    if (!sheet) {
      throw new Error(`Sheet "${SHEET_NAME}" not found`);
    }

    const dollarRate = sheet.getRange(2, 1).getValue();
    console.log('Dollar rate from A2:', dollarRate);

    if (!dollarRate || dollarRate <= 0) {
      throw new Error('Invalid dollar rate in A2. Please ensure A2 contains a valid number.');
    }

    let updatedCount = 0;
    let offCount = 0;

    for (const [realmKey, usdPrice] of Object.entries(priceData)) {
      const columnIndex = REALM_COLUMNS[realmKey.toLowerCase()];

      if (!columnIndex) {
        console.warn(`Unknown realm key: ${realmKey}`);
        continue;
      }

      const cell = sheet.getRange(PRICE_ROW, columnIndex);

      if (usdPrice !== null && usdPrice !== undefined && usdPrice > 0) {
        const discountedPrice = usdPrice * 0.91;
        const convertedPrice = Math.round(discountedPrice * dollarRate);

        cell.setValue(convertedPrice);
        cell.setBackground(GREEN_COLOR);
        cell.setFontColor('#000000');
        updatedCount++;
        console.log(`Updated ${realmKey}: USD ${usdPrice} → ${convertedPrice} (after -9% and ×${dollarRate})`);
      } else {
        cell.setValue('OFF');
        cell.setBackground(RED_COLOR);
        cell.setFontColor('#FFFFFF');
        offCount++;
        console.log(`Set ${realmKey} to OFF (no price available)`);
      }
    }

    if (updatedCount > 0) {
      const now = new Date();
      const utcTime = now.getTime() + (now.getTimezoneOffset() * 60000);
      const tehranTime = new Date(utcTime + (3.5 * 60 * 60 * 1000));
      const timeString = tehranTime.getHours().toString().padStart(2, '0') + ':' +
                        tehranTime.getMinutes().toString().padStart(2, '0');

      const timestampCell = sheet.getRange(PRICE_ROW, 1);
      timestampCell.setValue(timeString);
      timestampCell.setFontSize(10);
      timestampCell.setFontColor('#000000');
      timestampCell.setBackground('#E8F5E8');
    }

    const message = `Updated ${updatedCount} prices, ${offCount} set to OFF`;
    console.log(message);

    return {
      success: true,
      message: message,
      updatedCount: updatedCount,
      offCount: offCount
    };

  } catch (error) {
    console.error('Error updating sheet:', error);
    return {
      success: false,
      message: error.toString()
    };
  }
}

/**
 * Get Iranian shops from sheet
 */
function getIranianShops() {
  try {
    const sheet = SpreadsheetApp.openById(SHEET_ID).getSheetByName(CATA_SHEET_NAME);
    const iranianShops = [];

    for (let row = 19; row <= 28; row++) {
      const shopName = sheet.getRange(row, 1).getValue();
      if (shopName && typeof shopName === 'string' && shopName.trim()) {
        iranianShops.push(shopName.trim().toLowerCase());
      }
    }

    return iranianShops;
  } catch (error) {
    console.error('error getting iranian shops:', error);
    return [];
  }
}

/**
 * Check if shop is Iranian
 */
function isIranianShop(shopName, iranianShops) {
  if (!shopName || !iranianShops.length) return false;

  const cleanShopName = shopName.toLowerCase().trim();

  return iranianShops.some(iranianShop => {
    if (cleanShopName === iranianShop) return true;
    if (cleanShopName.includes(iranianShop) || iranianShop.includes(cleanShopName)) return true;
    return false;
  });
}

/**
 * Update Cata sheet with detailed realm reports
 */
function updateCataReports() {
  try {
    console.log('=== Starting Cata Reports Update ===');

    const spreadsheet = SpreadsheetApp.openById(SHEET_ID);
    const cataSheet = spreadsheet.getSheetByName(CATA_SHEET_NAME);

    if (!cataSheet) {
      throw new Error(`Cata sheet "${CATA_SHEET_NAME}" not found`);
    }

    const pageSizeCell = cataSheet.getRange(3, 1);
    let pageSize = pageSizeCell.getValue();
    if (!pageSize || pageSize <= 0) {
      pageSize = 20;
      pageSizeCell.setValue(pageSize);
    }

    const thresholdCell = cataSheet.getRange(5, 1);
    let undercutThreshold = thresholdCell.getValue();
    if (!undercutThreshold || undercutThreshold < 0) {
      undercutThreshold = 0;
      thresholdCell.setValue(undercutThreshold);
    }

    const mainSheet = spreadsheet.getSheetByName(SHEET_NAME);
    const dollarRate = mainSheet.getRange(2, 1).getValue();

    if (!dollarRate || dollarRate <= 0) {
      throw new Error('Invalid dollar rate in main sheet A2');
    }

    console.log(`Using page size: ${pageSize}, dollar rate: ${dollarRate}`);

    let totalUpdated = 0;

    for (const [realmKey, realmConfig] of Object.entries(CATA_REALMS)) {
      console.log(`Processing ${realmKey}...`);

      try {
        const realmData = fetchRealmData(realmConfig.url, pageSize);
        const updatedCount = updateRealmColumn(cataSheet, realmConfig.column, realmData, dollarRate, undercutThreshold);
        totalUpdated += updatedCount;

        console.log(`✓ ${realmKey}: Updated ${updatedCount} entries`);
        Utilities.sleep(100);
      } catch (error) {
        console.error(`✗ Error processing ${realmKey}:`, error);
      }
    }

    const now = new Date();
    const utcTime = now.getTime() + (now.getTimezoneOffset() * 60000);
    const tehranTime = new Date(utcTime + (3.5 * 60 * 60 * 1000));
    const timeString = tehranTime.getHours().toString().padStart(2, '0') + ':' +
                      tehranTime.getMinutes().toString().padStart(2, '0');

    const timestampCell = cataSheet.getRange(1, 1);
    timestampCell.setValue(timeString);
    timestampCell.setFontSize(12);
    timestampCell.setFontColor('#333333');
    timestampCell.setFontWeight('bold');

    const message = `Cata reports updated: ${totalUpdated} total entries across ${Object.keys(CATA_REALMS).length} realms`;
    console.log(message);

    updateRecommendedPositions();

    return {
      success: true,
      message: message,
      totalUpdated: totalUpdated
    };

  } catch (error) {
    console.error('Error updating Cata reports:', error);
    return {
      success: false,
      message: error.toString()
    };
  }
}

/**
 * Fetch data from G2G API for a specific realm
 */
function fetchRealmData(url, pageSize = 20) {
  console.log(`Fetching data from ${url} with page size ${pageSize}`);

  if (!url.includes('page_size=')) {
    url += (url.includes('?') ? '&' : '?') + `page_size=${pageSize}`;
  } else {
    url = url.replace(/page_size=\d+/, `page_size=${pageSize}`);
  }

  const response = UrlFetchApp.fetch(url, {
    muteHttpExceptions: true,
    headers: {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
  });

  if (response.getResponseCode() !== 200) {
    throw new Error(`API request failed with status ${response.getResponseCode()}`);
  }

  const data = JSON.parse(response.getContentText());

  if (data.code !== 2000 || !data.payload || !data.payload.results) {
    throw new Error('Invalid API response format');
  }

  data.payload.results.sort((a, b) => {
    const priceA = a.converted_unit_price || 999;
    const priceB = b.converted_unit_price || 999;
    return priceA - priceB;
  });

  return data.payload.results;
}

/**
 * Format stock with K/M notation
 */
function formatStock(stock) {
  if (stock >= 1000000) {
    return (stock / 1000000).toFixed(1) + 'M';
  } else if (stock >= 1000) {
    return (stock / 1000).toFixed(1) + 'K';
  } else {
    return stock.toString();
  }
}

/**
 * Convert price to K format
 */
function formatConvertedPrice(price) {
  return (price / 1000).toFixed(1) + 'K';
}

/**
 * Update realm column with data
 */
function updateRealmColumn(sheet, column, realmData, dollarRate, undercutThreshold = 0) {
  const startRow = CATA_START_ROW;

  if (!realmData || realmData.length === 0) {
    console.log(`No data for column ${column}`);
    return 0;
  }

  console.log(`Updating column ${column} with ${realmData.length} entries`);

  const iranianShops = getIranianShops();

  const myShopPrices = [];
  for (const seller of realmData) {
    const shopName = (seller.username || '').toLowerCase();
    if (shopName.includes('bonyadi') || shopName.includes('miba')) {
      const usdPrice = seller.converted_unit_price || 0;
      const convertedPrice = (usdPrice * 0.91) * dollarRate;
      myShopPrices.push(convertedPrice);
    }
  }

  const values = [];
  const backgrounds = [];
  const fontWeights = [];

  for (let i = 0; i < realmData.length; i++) {
    const seller = realmData[i];
    const shopName = seller.username || 'Unknown';
    const stock = seller.available_qty || 0;
    const usdPrice = seller.converted_unit_price || 0;
    const convertedPrice = (usdPrice * 0.91) * dollarRate;

    const formattedShop = shopName.substring(0, 7).padEnd(7, ' ');
    const formattedStock = formatStock(stock).padStart(6, ' ');
    const formattedUSD = usdPrice.toFixed(6);
    const formattedConverted = formatConvertedPrice(convertedPrice);

    const displayText = `${formattedShop}|${formattedStock}|${formattedUSD}|${formattedConverted}`;
    values.push([displayText]);

    let backgroundColor = null;
    let fontWeight = 'normal';
    const shopNameLower = shopName.toLowerCase();

    if (shopNameLower.includes('bonyadi') || shopNameLower.includes('miba')) {
      backgroundColor = YELLOW_COLOR;
      fontWeight = 'bold';
    } else if (isIranianShop(shopName, iranianShops)) {
      backgroundColor = IRANIAN_HIGHLIGHT;
      fontWeight = 'bold';
    } else if (undercutThreshold > 0 && myShopPrices.length > 0) {
      const isUndercutting = myShopPrices.some(myPrice =>
        convertedPrice < myPrice && (myPrice - convertedPrice) <= undercutThreshold
      );
      if (isUndercutting) {
        backgroundColor = '#FFB6C1';
      }
    }

    if (!backgroundColor) {
      backgroundColor = (i % 2 === 0) ? '#FFFFFF' : LIGHT_GRAY;
    }

    backgrounds.push([backgroundColor]);
    fontWeights.push([fontWeight]);
  }

  if (values.length > 0) {
    try {
      const dataRange = sheet.getRange(startRow, column, values.length, 1);
      dataRange.setValues(values);
      dataRange.setBackgrounds(backgrounds);
      dataRange.setFontWeights(fontWeights);
      dataRange.setFontFamily('Consolas');
      dataRange.setFontSize(9);
      dataRange.setVerticalAlignment('middle');
      dataRange.setHorizontalAlignment('left');
      dataRange.setFontColor('#000000');
      dataRange.setBorder(true, true, true, true, true, true, '#CCCCCC', SpreadsheetApp.BorderStyle.SOLID);

      for (let i = 0; i < values.length; i++) {
        const cellRange = sheet.getRange(startRow + i, column);
        const text = values[i][0];
        const parts = text.split('|');
        if (parts.length === 4) {
          const richText = SpreadsheetApp.newRichTextValue()
            .setText(text)
            .setTextStyle(0, text.lastIndexOf('|'), SpreadsheetApp.newTextStyle().setForegroundColor('#000000').build())
            .setTextStyle(text.lastIndexOf('|') + 1, text.length, SpreadsheetApp.newTextStyle().setForegroundColor('#008000').build())
            .build();
          cellRange.setRichTextValue(richText);
        }
      }

      console.log(`Successfully updated ${values.length} entries in column ${column}`);

      const clearStartRow = startRow + values.length;
      const clearEndRow = startRow + 100;
      if (clearStartRow <= clearEndRow) {
        const clearRange = sheet.getRange(clearStartRow, column, clearEndRow - clearStartRow + 1, 1);
        clearRange.clearContent();
        clearRange.clearFormat();
      }

    } catch (error) {
      console.error(`Error updating column ${column}:`, error);
      throw error;
    }
  }

  return realmData.length;
}

/**
 * Get Miba position in recommended results
 */
function getMibaPosition(recommendedUrl) {
  try {
    const response = UrlFetchApp.fetch(recommendedUrl, {
      muteHttpExceptions: true,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });

    if (response.getResponseCode() !== 200) {
      console.error(`recommended api failed ${response.getResponseCode()}`);
      return null;
    }

    const data = JSON.parse(response.getContentText());

    if (data.code !== 2000 || !data.payload || !data.payload.results) {
      console.error('bad recommended api response');
      return null;
    }

    for (let i = 0; i < data.payload.results.length; i++) {
      const username = (data.payload.results[i].username || '').toLowerCase();
      if (username.includes('miba')) {
        return i + 1;
      }
    }

    return null;

  } catch (error) {
    console.error('error getting miba position:', error);
    return null;
  }
}

/**
 * Get color for position
 */
function getPositionColor(position) {
  if (!position || position > 10) {
    return '#CC0000';
  }

  const ratio = (10 - position) / 9;
  const red = Math.round(204 * (1 - ratio));
  const green = Math.round(204 * ratio);
  const blue = 0;

  return `rgb(${red}, ${green}, ${blue})`;
}

/**
 * Update recommended positions
 */
function updateRecommendedPositions() {
  try {
    console.log('updating recommended positions...');

    const spreadsheet = SpreadsheetApp.openById(SHEET_ID);
    const cataSheet = spreadsheet.getSheetByName(CATA_SHEET_NAME);

    if (!cataSheet) {
      throw new Error('cata sheet not found');
    }

    for (const [realmKey, realmConfig] of Object.entries(CATA_REALMS)) {
      if (!realmConfig.recommendedUrl) continue;

      console.log(`getting position for ${realmKey}...`);
      const position = getMibaPosition(realmConfig.recommendedUrl);

      const cell = cataSheet.getRange(4, realmConfig.column);

      if (position) {
        cell.setValue(position);
        cell.setBackground(getPositionColor(position));
        cell.setFontColor('#FFFFFF');
        cell.setFontWeight('bold');
        console.log(`${realmKey}: position ${position}`);
      } else {
        cell.setValue('N/A');
        cell.setBackground('#CCCCCC');
        cell.setFontColor('#000000');
        console.log(`${realmKey}: miba not found`);
      }

      Utilities.sleep(500);
    }

    console.log('recommended positions updated');
    return true;

  } catch (error) {
    console.error('error updating recommended positions:', error);
    return false;
  }
}
