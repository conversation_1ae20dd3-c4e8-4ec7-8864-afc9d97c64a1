# G2G Price Updater - Deployment Guide

This guide will help you separate your code into a standalone Apps Script project to hide the implementation from other users.

## Overview

The solution splits your code into two parts:
1. **Standalone Service** (`standalone-service.gs`) - Contains all the business logic and API calls
2. **Main Sheet Client** (`main-sheet-client.gs`) - Minimal code that forwards requests to the standalone service

## Step 1: Create the Standalone Apps Script Project

1. Go to [Google Apps Script](https://script.google.com)
2. Click "New Project"
3. Replace the default `Code.gs` content with the code from `standalone-service.gs`
4. Save the project with a name like "G2G Price Service"

## Step 2: Deploy the Standalone Service as Web App

1. In your standalone Apps Script project, click "Deploy" → "New deployment"
2. Choose type: "Web app"
3. Set the following:
   - **Description**: "G2G Price Service"
   - **Execute as**: "Me"
   - **Who has access**: "Anyone" (this allows your main sheet to call it)
4. Click "Deploy"
5. **IMPORTANT**: Copy the Web App URL - you'll need this for Step 4

## Step 3: Update Your Main Sheet Code

1. Go to your main Google Sheet
2. Open Apps Script (Extensions → Apps Script)
3. **Replace ALL existing code** with the content from `main-sheet-client.gs`
4. Save the project

## Step 4: Configure the Connection

1. In your main sheet's Apps Script, find this line:
   ```javascript
   const STANDALONE_SERVICE_URL = 'YOUR_STANDALONE_SERVICE_URL';
   ```
2. Replace `'YOUR_STANDALONE_SERVICE_URL'` with the Web App URL you copied in Step 2
3. Save the file

## Step 5: Test the Setup

1. In your main sheet's Apps Script, run the `testConnection()` function
2. Check the logs to ensure it connects successfully
3. Try running `updatePricesManual()` or `updateCataManual()` to test functionality

## Step 6: Set Up Triggers (Optional)

If you want automatic updates, you can set up time-based triggers in the **standalone service**:

1. Go to your standalone Apps Script project
2. Click on "Triggers" (clock icon)
3. Add triggers for functions like `updateCataReports()` or `updateRecommendedPositions()`

## What Each File Does

### `standalone-service.gs`
- Contains all your original business logic
- Handles G2G API calls
- Updates spreadsheet data
- Processes Cata reports
- Manages recommended positions
- Deployed as a web app that other scripts can call

### `main-sheet-client.gs`
- Minimal code visible to other users
- Forwards requests to the standalone service
- Handles only the cell selection functionality locally (for performance)
- Acts as a proxy between your Chrome extension and the standalone service

## Security Benefits

1. **Hidden Implementation**: Other users can't see your API logic, URLs, or business rules
2. **Centralized Updates**: You can update the standalone service without touching the main sheet
3. **Access Control**: You control who can access the standalone service
4. **Separation of Concerns**: Main sheet only handles display, standalone service handles processing

## Maintenance

- **To update logic**: Modify the standalone service and redeploy
- **To update display**: Modify the main sheet client
- **To add new features**: Add to standalone service, then update client if needed

## Troubleshooting

### Connection Issues
- Ensure the Web App URL is correct in `main-sheet-client.gs`
- Check that the standalone service is deployed with "Anyone" access
- Verify the standalone service is published and not in draft mode

### Permission Issues
- Make sure both scripts have necessary permissions
- Re-authorize if prompted
- Check that the standalone service can access your spreadsheet

### Function Not Working
- Check logs in both the main sheet and standalone service
- Ensure the request format matches what the standalone service expects
- Test individual functions in the standalone service first

## Chrome Extension Compatibility

Your existing Chrome extension should work without changes since the main sheet still exposes the same `doPost()` and `doGet()` endpoints. The client simply forwards requests to the standalone service.
