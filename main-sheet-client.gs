// Minimal client code for main sheet
// Replace YOUR_STANDALONE_SERVICE_URL with your deployed web app URL

const STANDALONE_SERVICE_URL = 'YOUR_STANDALONE_SERVICE_URL'; // Replace with your deployed web app URL
const SHEET_ID = '1FStcYoFPninNRsWX4XvXxHVtrc2usOnJ79R-ep3l38c';
const SHEET_NAME = 'Sheet1';
const CATA_SHEET_NAME = 'Cata';

/**
 * Main function to handle POST requests from Chrome extension
 */
function doPost(e) {
  try {
    console.log('Received POST request - forwarding to standalone service');
    
    // Forward the request to standalone service
    const response = UrlFetchApp.fetch(STANDALONE_SERVICE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      payload: e.postData.contents,
      muteHttpExceptions: true
    });
    
    if (response.getResponseCode() !== 200) {
      throw new Error(`Standalone service returned status ${response.getResponseCode()}`);
    }
    
    const result = JSON.parse(response.getContentText());
    
    return ContentService
      .createTextOutput(JSON.stringify(result))
      .setMimeType(ContentService.MimeType.JSON);
      
  } catch (error) {
    console.error('Error in doPost:', error);
    
    return ContentService
      .createTextOutput(JSON.stringify({
        success: false,
        error: error.toString()
      }))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

/**
 * Handle GET requests
 */
function doGet(e) {
  try {
    // Forward GET request to standalone service
    const response = UrlFetchApp.fetch(STANDALONE_SERVICE_URL, {
      method: 'GET',
      muteHttpExceptions: true
    });
    
    if (response.getResponseCode() !== 200) {
      throw new Error(`Standalone service returned status ${response.getResponseCode()}`);
    }
    
    return ContentService
      .createTextOutput(response.getContentText())
      .setMimeType(ContentService.MimeType.JSON);
      
  } catch (error) {
    console.error('Error in doGet:', error);
    
    return ContentService
      .createTextOutput(JSON.stringify({
        status: 'G2G Price Updater Client is running',
        error: error.toString(),
        timestamp: new Date().toISOString()
      }))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

/**
 * Manual function to update prices (calls standalone service)
 */
function updatePricesManual() {
  try {
    const requestData = {
      action: 'updatePrices',
      data: {
        'spine h': 0.001234,
        'spine a': null,
        'living a': 0.001456,
        'thunder a': 0.001789,
        'living h': null,
        'thunder h': 0.001890,
        'wild a': 0.001567
      }
    };
    
    const response = UrlFetchApp.fetch(STANDALONE_SERVICE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      payload: JSON.stringify(requestData),
      muteHttpExceptions: true
    });
    
    if (response.getResponseCode() !== 200) {
      throw new Error(`Service returned status ${response.getResponseCode()}`);
    }
    
    const result = JSON.parse(response.getContentText());
    console.log('Manual update result:', result);
    return result;
    
  } catch (error) {
    console.error('Error in manual update:', error);
    return { success: false, error: error.toString() };
  }
}

/**
 * Manual function to update Cata reports (calls standalone service)
 */
function updateCataManual() {
  try {
    const requestData = {
      action: 'updateCataReports'
    };
    
    const response = UrlFetchApp.fetch(STANDALONE_SERVICE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      payload: JSON.stringify(requestData),
      muteHttpExceptions: true
    });
    
    if (response.getResponseCode() !== 200) {
      throw new Error(`Service returned status ${response.getResponseCode()}`);
    }
    
    const result = JSON.parse(response.getContentText());
    console.log('Manual Cata update result:', result);
    return result;
    
  } catch (error) {
    console.error('Error in manual Cata update:', error);
    return { success: false, error: error.toString() };
  }
}

/**
 * Handle cell selection for undercut copying - minimal local functionality
 */
function onSelectionChange(e) {
  try {
    console.log('onSelectionChange triggered');

    if (!e || !e.source || !e.range) {
      console.log('missing event data');
      return;
    }

    const sheet = e.source.getActiveSheet();
    const range = e.range;

    console.log(`sheet: ${sheet.getName()}, row: ${range.getRow()}, col: ${range.getColumn()}`);

    // Only handle clicks on Cata sheet
    if (sheet.getName() !== CATA_SHEET_NAME) {
      console.log('not cata sheet');
      return;
    }

    // Only handle single cell selection starting from row 3
    if (range.getNumRows() !== 1 || range.getNumColumns() !== 1 || range.getRow() < 3) {
      console.log('wrong cell selection');
      return;
    }

    const cellValue = range.getValue();
    console.log(`cell value: ${cellValue}`);

    if (!cellValue || typeof cellValue !== 'string') {
      console.log('no string value');
      return;
    }

    // Check if it's a formatted price cell (contains |)
    if (!cellValue.includes('|')) {
      console.log('no pipe in cell');
      return;
    }

    // Extract USD price from format: "ShopName|Stock|USDPrice|ConvertedPrice"
    const parts = cellValue.split('|');
    if (parts.length !== 4) {
      console.log(`wrong parts count: ${parts.length}`);
      return;
    }

    const usdPrice = parseFloat(parts[2]);
    if (isNaN(usdPrice)) {
      console.log(`bad usd price: ${parts[2]}`);
      return;
    }

    // Calculate undercut price (reduce by 0.000001)
    const undercutPrice = (usdPrice - 0.000001).toFixed(6);
    console.log(`undercut price: ${undercutPrice}`);

    // Put undercut price in A9
    const copyCell = sheet.getRange(9, 1);
    copyCell.setValue(undercutPrice);
    copyCell.setBackground('#90EE90');
    copyCell.setFontWeight('bold');

    console.log('A9 updated successfully');

  } catch (error) {
    console.error('Error in onSelectionChange:', error);
  }
}

/**
 * Setup function - install triggers
 */
function setup() {
  console.log('setup done');
  try {
    const sheet = SpreadsheetApp.openById(SHEET_ID).getSheetByName(SHEET_NAME);
    const cataSheet = SpreadsheetApp.openById(SHEET_ID).getSheetByName(CATA_SHEET_NAME);

    // Install onSelectionChange trigger
    installTriggers();

    return true;
  } catch (error) {
    console.error('sheet access failed:', error);
    return false;
  }
}

/**
 * Install triggers for onSelectionChange
 */
function installTriggers() {
  const ss = SpreadsheetApp.openById(SHEET_ID);

  // Delete old triggers first
  const triggers = ScriptApp.getProjectTriggers();
  triggers.forEach(trigger => {
    if (trigger.getHandlerFunction() === 'onSelectionChange') {
      ScriptApp.deleteTrigger(trigger);
    }
  });

  // Install new trigger
  ScriptApp.newTrigger('onSelectionChange')
    .spreadsheet(ss)
    .onSelectionChange()
    .create();

  console.log('onSelectionChange trigger installed');
}

/**
 * Test connection to standalone service
 */
function testConnection() {
  try {
    const response = UrlFetchApp.fetch(STANDALONE_SERVICE_URL, {
      method: 'GET',
      muteHttpExceptions: true
    });
    
    console.log(`Connection test: Status ${response.getResponseCode()}`);
    console.log(`Response: ${response.getContentText()}`);
    
    return response.getResponseCode() === 200;
    
  } catch (error) {
    console.error('Connection test failed:', error);
    return false;
  }
}
